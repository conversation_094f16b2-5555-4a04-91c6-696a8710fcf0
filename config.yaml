# WNN-MRNN模型配置文件 - 支持多种数据集类型

# 模型参数
model:
  in_channels: 2        # 输入通道数（I/Q通道）
  num_classes: 26       # 类别数量，会根据数据集自动调整
  sequence_length: 1024 # 序列长度，会根据数据集自动调整

  # 通用模型参数（默认值，会被数据集特定参数覆盖）
  msb_depth: 1          # MSB内部深度
  d_state: 16           # Mamba状态空间维度
  d_conv: 4             # Mamba卷积核大小
  expand: 2             # Mamba扩展因子

  # 数据集特定的模型参数 - 根据不同数据集自动调整
  dataset_specific_params:
    rml:
      wavelet_dim: 64
      rnn_dim: 64
      num_layers: 2
      num_levels: 1
      dropout: 0.5
      batch_size: 64
    rml201801a:
      wavelet_dim: 64
      rnn_dim: 64
      num_layers: 3
      num_levels: 2
      dropout: 0.5
      batch_size: 512
    hisar:
      wavelet_dim: 32
      rnn_dim: 256
      num_layers: 4
      num_levels: 2
      dropout: 0.159
      batch_size: 128
    torchsig1024:
      wavelet_dim: 32
      rnn_dim: 256
      num_layers: 4
      num_levels: 2
      dropout: 0.159
      batch_size: 64
    torchsig2048:
      wavelet_dim: 64
      rnn_dim: 128
      num_layers: 4
      num_levels: 1
      dropout: 0.159
      batch_size: 64
    torchsig4096:
      wavelet_dim: 128
      rnn_dim: 128
      num_layers: 4
      num_levels: 1
      dropout: 0.159
      batch_size: 16

# 数据参数
data:
  dataset_type: 'rml'   # 可选: 'rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'

  # 数据集特定配置
  dataset_configs:
    rml:
      file_path: 'data/RML2016.10a_dict.pkl'
      sequence_length: 128
      snr_range: [-20, 18]
      modulations: null    # null表示使用所有调制类型
      stratify_by_snr: true
      samples_per_key: null
    rml201801a:
      file_path: 'data/GOLD_XYZ_OSC.0001_1024.hdf5'
      sequence_length: 1024
      snr_range: [-20, 30]
      modulations: null    # null表示使用所有24种调制类型
      use_all_snr: true    # 是否使用所有SNR级别的数据
    hisar:
      sequence_length: 1024
      snr_range: [-20, 18]
      train_path: 'data/hisar/train_data.mat'
      train_labels_path: 'data/hisar/train_labels.csv'
      train_snr_path: 'data/hisar/train_snr.csv'
      test_path: 'data/hisar/test_data.mat'
      test_labels_path: 'data/hisar/test_labels.csv'
      test_snr_path: 'data/hisar/test_snr.csv'
    torchsig1024:
      sequence_length: 1024
      snr_range: [0, 30]
      train_path: 'data/torchsig1024/train_data.mat'
      train_labels_path: 'data/torchsig1024/train_labels.csv'
      train_snr_path: 'data/torchsig1024/train_snr.csv'
      test_path: 'data/torchsig1024/test_data.mat'
      test_labels_path: 'data/torchsig1024/test_labels.csv'
      test_snr_path: 'data/torchsig1024/test_snr.csv'
    torchsig2048:
      sequence_length: 2048
      snr_range: [0, 30]
      train_path: 'data/torchsig2048/train_data.mat'
      train_labels_path: 'data/torchsig2048/train_labels.csv'
      train_snr_path: 'data/torchsig2048/train_snr.csv'
      test_path: 'data/torchsig2048/test_data.mat'
      test_labels_path: 'data/torchsig2048/test_labels.csv'
      test_snr_path: 'data/torchsig2048/test_snr.csv'
    torchsig4096:
      sequence_length: 4096
      snr_range: [0, 30]
      train_path: 'data/torchsig4096/train_data.mat'
      train_labels_path: 'data/torchsig4096/train_labels.csv'
      train_snr_path: 'data/torchsig4096/train_snr.csv'
      test_path: 'data/torchsig4096/test_data.mat'
      test_labels_path: 'data/torchsig4096/test_labels.csv'
      test_snr_path: 'data/torchsig4096/test_snr.csv'

  # 通用数据处理参数
  train_ratio: 0.66     # 训练集比例 (70%)
  test_ratio: 0.19     # 测试集比例 (15%)
  val_ratio: 0.15      # 验证集比例 (15%)

# 训练参数
training:
  # 基础训练参数
  batch_size: 128         # 默认批处理大小（会被数据集特定参数覆盖）
  epochs: 200
  learning_rate: 0.001
  weight_decay: 0.0001
  lambda_lifting: 0.1  # 小波提升损失权重
  seed: 4242             # 随机种子
  device: 'cuda'          # 使用'cuda'或'cpu'

  # 学习率调度
  scheduler: "plateau"     # 学习率调度器类型
  min_lr: 0.00001         # 最小学习率
  patience: 5            # 学习率调整耐心值

  # 早停和监控
  early_stopping: true    # 是否启用早停
  early_stop_patience: 15 # 早停耐心值

  # 优化技巧
  clip_grad: 1.0          # 梯度裁剪阈值

  # 数据加载优化
  num_workers: 8         # 数据加载线程数
  pin_memory: true        # 是否使用锁页内存
  prefetch_factor: 8      # 预取因子
  persistent_workers: true # 使用持久化工作进程
  preload_to_memory: false # 是否预加载数据到内存（大数据集建议false）

# 输出设置
output_dir: './saved_models/wnn_mrnn'

# 数据集类别名称定义
class_names:
  # RML数据集的11种调制类型
  rml:
    - "8PSK"
    - "AM-DSB"
    - "AM-SSB"
    - "BPSK"
    - "CPFSK"
    - "GFSK"
    - "PAM4"
    - "QAM16"
    - "QAM64"
    - "QPSK"
    - "WBFM"

  # RML2018.01a数据集的24种调制类型
  rml201801a:
    - "OOK"
    - "4ASK"
    - "8ASK"
    - "BPSK"
    - "QPSK"
    - "8PSK"
    - "16PSK"
    - "32PSK"
    - "16APSK"
    - "32APSK"
    - "64APSK"
    - "128APSK"
    - "16QAM"
    - "32QAM"
    - "64QAM"
    - "128QAM"
    - "256QAM"
    - "AM-SSB-WC"
    - "AM-SSB-SC"
    - "AM-DSB-WC"
    - "AM-DSB-SC"
    - "FM"
    - "GMSK"
    - "OQPSK"

  # HisarMod数据集的26种调制类型
  hisar:
    - "BPSK"
    - "QPSK"
    - "8PSK"
    - "16PSK"
    - "32PSK"
    - "64PSK"
    - "4QAM"
    - "8QAM"
    - "16QAM"
    - "32QAM"
    - "64QAM"
    - "128QAM"
    - "256QAM"
    - "2FSK"
    - "4FSK"
    - "8FSK"
    - "16FSK"
    - "4PAM"
    - "8PAM"
    - "16PAM"
    - "AM-DSB"
    - "AM-DSB-SC"
    - "AM-USB"
    - "AM-LSB"
    - "FM"
    - "PM"

  # TorchSig数据集的25种调制类型（所有torchsig变体共享）
  torchsig:
    - "BPSK"
    - "QPSK"
    - "8PSK"
    - "16PSK"
    - "32PSK"
    - "64PSK"
    - "16QAM"
    - "32QAM"
    - "64QAM"
    - "256QAM"
    - "2FSK"
    - "4FSK"
    - "8FSK"
    - "16FSK"
    - "4ASK"
    - "8ASK"
    - "16ASK"
    - "32ASK"
    - "64ASK"
    - "AM-DSB"
    - "AM-DSB-SC"
    - "AM-USB"
    - "AM-LSB"
    - "FM"
    - "OOK"

# ========== 向后兼容配置 ==========
# 以下配置保持与原有代码的兼容性，避免修改现有代码

# 向后兼容：旧的类别名称格式
rml_class_names:
  - "8PSK"
  - "AM-DSB"
  - "AM-SSB"
  - "BPSK"
  - "CPFSK"
  - "GFSK"
  - "PAM4"
  - "QAM16"
  - "QAM64"
  - "QPSK"
  - "WBFM"

rml201801a_class_names:
  - "OOK"
  - "4ASK"
  - "8ASK"
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "16APSK"
  - "32APSK"
  - "64APSK"
  - "128APSK"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "128QAM"
  - "256QAM"
  - "AM-SSB-WC"
  - "AM-SSB-SC"
  - "AM-DSB-WC"
  - "AM-DSB-SC"
  - "FM"
  - "GMSK"
  - "OQPSK"

hisar_class_names:
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "64PSK"
  - "4QAM"
  - "8QAM"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "128QAM"
  - "256QAM"
  - "2FSK"
  - "4FSK"
  - "8FSK"
  - "16FSK"
  - "4PAM"
  - "8PAM"
  - "16PAM"
  - "AM-DSB"
  - "AM-DSB-SC"
  - "AM-USB"
  - "AM-LSB"
  - "FM"
  - "PM"

torchsig_class_names:
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "64PSK"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "256QAM"
  - "2FSK"
  - "4FSK"
  - "8FSK"
  - "16FSK"
  - "4ASK"
  - "8ASK"
  - "16ASK"
  - "32ASK"
  - "64ASK"
  - "AM-DSB"
  - "AM-DSB-SC"
  - "AM-USB"
  - "AM-LSB"
  - "FM"
  - "OOK"

# 向后兼容：旧的数据配置格式
# RML数据集配置
rml_file_path: 'data/RML2016.10a_dict.pkl'
modulations: null
snr_range: [-20, 18]
stratify_by_snr: true
samples_per_key: null

# RML2018.01a数据集配置
rml201801a_file_path: 'data/GOLD_XYZ_OSC.0001_1024.hdf5'
rml201801a_modulations: null
rml201801a_use_all_snr: true

# HisarMod数据集配置
train_path: 'data/hisar/train_data.mat'
train_labels_path: 'data/hisar/train_labels.csv'
train_snr_path: 'data/hisar/train_snr.csv'
test_path: 'data/hisar/test_data.mat'
test_labels_path: 'data/hisar/test_labels.csv'
test_snr_path: 'data/hisar/test_snr.csv'

# TorchSig数据集配置
torchsig1024_train_path: 'data/torchsig1024/train_data.mat'
torchsig1024_train_labels_path: 'data/torchsig1024/train_labels.csv'
torchsig1024_train_snr_path: 'data/torchsig1024/train_snr.csv'
torchsig1024_test_path: 'data/torchsig1024/test_data.mat'
torchsig1024_test_labels_path: 'data/torchsig1024/test_labels.csv'
torchsig1024_test_snr_path: 'data/torchsig1024/test_snr.csv'

torchsig2048_train_path: 'data/torchsig2048/train_data.mat'
torchsig2048_train_labels_path: 'data/torchsig2048/train_labels.csv'
torchsig2048_train_snr_path: 'data/torchsig2048/train_snr.csv'
torchsig2048_test_path: 'data/torchsig2048/test_data.mat'
torchsig2048_test_labels_path: 'data/torchsig2048/test_labels.csv'
torchsig2048_test_snr_path: 'data/torchsig2048/test_snr.csv'

torchsig4096_train_path: 'data/torchsig4096/train_data.mat'
torchsig4096_train_labels_path: 'data/torchsig4096/train_labels.csv'
torchsig4096_train_snr_path: 'data/torchsig4096/train_snr.csv'
torchsig4096_test_path: 'data/torchsig4096/test_data.mat'
torchsig4096_test_labels_path: 'data/torchsig4096/test_labels.csv'
torchsig4096_test_snr_path: 'data/torchsig4096/test_snr.csv'

# 向后兼容：旧的序列长度和SNR范围配置
sequence_lengths:
  rml: 128
  rml201801a: 1024
  hisar: 1024
  torchsig1024: 1024
  torchsig2048: 2048
  torchsig4096: 4096

snr_ranges:
  rml: [-20, 18]
  rml201801a: [-20, 30]
  hisar: [-20, 18]
  torchsig1024: [0, 30]
  torchsig2048: [0, 30]
  torchsig4096: [0, 30]
